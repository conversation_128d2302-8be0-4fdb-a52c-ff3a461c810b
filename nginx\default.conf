server {
    listen       9800;
    listen  [::]:9800;
    server_name  localhost;
    access_log /var/log/nginx/default.access.log;
    error_log /var/log/nginx/default.error.log;

    location / {
        root    /usr/share/nginx/sz-admin;
        try_files $uri $uri/ /index.html;
        index  index.html;
    }

    location /api {
        client_max_body_size 200M;  # 设置最大文件上传大小
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_pass $NGINX_PROXY_PASS;
        proxy_pass http://sz-service-admin:9991/api;
    }

    # websocket 支持，如不需要请手动注释
    location /socket {
        proxy_pass http://sz-service-websocket:9993; # 后端 WebSocket 服务器的地址
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        proxy_read_timeout 3600s;  # 设置为1小时
        proxy_send_timeout 3600s;  # 设置为1小时
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/sz-admin;
    }
}
