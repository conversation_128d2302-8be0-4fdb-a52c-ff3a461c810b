.sliderBox {
  user-select: none;
  width: 320px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.2); // 盒阴影增强立体感
  border-radius: 4px;
}

.sliderBox_title {
  position: relative;
  font-size: 15px;
  text-align: center; // 居中标题
}

.sliderBox_refresh {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 18px;
  height: 18px;
  cursor: pointer;
  color: #ffffff;
  transition: color 0.3s; // 添加过渡效果
  &:hover {
    color: #007bff; // 悬停时改变颜色
  }
  .el-icon {
    width: 100%;
    height: 100%;
    font-size: 18px; // 确保图标大小一致
  }
}

.sliderBox_content {
  position: relative;
  width: 100%;
  height: 160px;
  border: 1px solid #c6dbf5; // 使用较浅的蓝色边框

  .bigImg {
    width: 100%;
    height: 100%;
    object-fit: cover; // 确保图片覆盖整个容器
  }

  .smallImg {
    position: absolute;
    width: 50px;
    height: 50px;
    cursor: pointer;
  }
}

.btnBox {
  position: relative;
  width: 100%;
  height: 30px;
  margin-top: 22px;
  background-color: var(--el-color-primary-light-7); // 使用Element Plus的浅色主色
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  .sliderBox_text {
    font-size: 14px;
    color: #333;
  }

  .sliderBox_track {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: var(--el-color-primary-light-2); // 使用浅色主色
    border-radius: 8px;
  }

  .sliderBox_btn {
    position: absolute;
    top: -5px;
    left: 0;
    width: 50px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    background-color: var(--el-color-primary); // 使用主色
    border-radius: 8px;
    box-shadow: 0 0 6px #ce9f7f; // 适度阴影
    cursor: pointer;
  }
}

.overlay {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.5); // 半透明黑色背景
  transition: background-color 0.3s; // 添加过渡效果
  &.success {
    background-color: rgba(92, 184, 92, 0.5); // 成功时的背景颜色
  }
  &.failure {
    background-color: rgba(217, 83, 79, 0.5); // 失败时的背景颜色
  }
}

.sliderBox_btn {
  cursor: pointer;
  user-select: none;
  outline: none;
}
.sliderBox_btn:active {
  background: #eee;
}
