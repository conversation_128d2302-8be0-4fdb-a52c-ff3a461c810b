import type { IPageQuery } from '@/api/types'

// 查询条件
export type BizPubContentQuery = IPageQuery & {
    reviewStatus?: string
    pubContent?: string
    createTime?: string
  }

// 编辑form表单
export type BizPubContentForm = {
    reviewStatus?: string
    pubContent?: string
 }

// list或detail返回结构
export type BizPubContentRow = {
    reviewStatus?: string
    pubContent?: string
    createId?: number
    createTime?: string
    updateTime?: string
  }

