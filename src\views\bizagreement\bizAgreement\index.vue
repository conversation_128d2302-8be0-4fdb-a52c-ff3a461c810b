<template>
  <div class="table-box">
    <ProTable
      ref="proTableRef"
      title="公示/协议内容表"
      :indent="20"
      :columns="columns"
      :search-columns="searchColumns"
      :request-api="getTableList"
      row-key="agreementId"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary"
          v-auth="'biz.agreement.create'"
          :icon="CirclePlus"
          @click="openAddEdit('新增公示/协议内容表')"
        >
          新增
        </el-button>
        <el-button
          v-auth="'biz.agreement.remove'"
          type="danger"
          :icon="Delete"
          plain
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          批量删除
        </el-button>
        <el-button
          v-auth="'biz.agreement.import'"
          type="primary"
          :icon="Upload"
          plain
          @click="importData"
        >
          导入
        </el-button>
        <el-button
          v-auth="'biz.agreement.export'"
          type="primary"
          :icon="Download"
          plain
          @click="downloadFile"
        >
          导出
        </el-button>
      </template>
      <template #operation="{ row }">
        <el-button
          v-auth="'biz.agreement.update'"
          type="primary"
          link
          :icon="EditPen"
          @click="openAddEdit('编辑公示/协议内容表', row, false)"
        >
          编辑
        </el-button>
        <el-button
            v-auth="'biz.agreement.remove'"
          type="primary"
          link
          :icon="Delete"
          @click="deleteInfo(row)"
        >
          删除
        </el-button>
      </template>
    </ProTable>
    <BizAgreementForm ref="bizAgreementRef" />
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  CirclePlus,
  Delete,
  EditPen,
  Upload,
  Download,
} from '@element-plus/icons-vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  createBizAgreementApi,
  removeBizAgreementApi,
  updateBizAgreementApi,
  getBizAgreementListApi,
  getBizAgreementDetailApi,
  importBizAgreementExcelApi,
  exportBizAgreementExcelApi,
} from '@/api/modules/bizagreement/bizAgreement';
import { useHandleData } from '@/hooks/useHandleData';
import BizAgreementForm from '@/views/bizagreement/bizAgreement/components/BizAgreementForm.vue';
import type { ColumnProps, ProTableInstance, SearchProps } from '@/components/ProTable/interface';
import type { BizAgreementQuery, BizAgreementRow } from '@/api/types/bizagreement/bizAgreement';
import ImportExcel from '@/components/ImportExcel/index.vue';
import { downloadTemplate } from '@/api/modules/system/common';
import { useDownload } from "@/hooks/useDownload";
defineOptions({
  name: 'BizAgreementView'
})
const proTableRef = ref<ProTableInstance>();
// 表格配置项
const columns: ColumnProps<BizAgreementRow>[] = [
  { type: 'selection', width: 80 },
  { prop: 'agreementContent', label: '公示/协议 内容' },
  { prop: 'isNot', label: '启用/禁用' },
  { prop: 'remark', label: '备注' },
  { prop: 'sortNum', label: '排序' },
  { prop: 'operation', label: '操作', width: 250, fixed: 'right' }
]
// 搜索条件项
const searchColumns: SearchProps[] = [
  { prop: 'agreementContent', label: '公示/协议 内容', el: 'input' },
  { prop: 'isNot', label: '启用/禁用', el: 'input' },
  { prop: 'sortNum', label: '排序', el: 'input' },
]
// 获取table列表
const getTableList = (params: BizAgreementQuery) => {
  let newParams = formatParams(params);
  return getBizAgreementListApi(newParams);
};
const formatParams = (params: BizAgreementQuery) =>{
  let newParams = JSON.parse(JSON.stringify(params));
  if(newParams.createTime) {
    newParams.createTimeStart = newParams.createTime[0];
    newParams.createTimeEnd = newParams.createTime[1];
    delete newParams.createTime;
  }

  if(newParams.updateTime) {
    newParams.updateTimeStart = newParams.updateTime[0];
    newParams.updateTimeEnd = newParams.updateTime[1];
    delete newParams.updateTime;
  }

  return newParams;
}
// 打开 drawer(新增、查看、编辑)
const bizAgreementRef = ref<InstanceType<typeof BizAgreementForm>>()
const openAddEdit = async(title: string, row: any = {}, isAdd = true) => {
  if (!isAdd) {
    const record = await getBizAgreementDetailApi({ id: row?.agreementId })
    row = record?.data
  }
  const params = {
    title,
    row: { ...row },
    api: isAdd ? createBizAgreementApi : updateBizAgreementApi,
    getTableList: proTableRef.value?.getTableList
  }
  bizAgreementRef.value?.acceptParams(params)
}
// 删除信息
const deleteInfo = async (params: BizAgreementRow) => {
  await useHandleData(
    removeBizAgreementApi,
    { ids: [params.agreementId] },
    `删除【${params.agreementId}】公示/协议内容表`
  )
  proTableRef.value?.getTableList()
}
// 批量删除信息
const batchDelete = async (ids: (string | number)[]) => {
  await useHandleData(removeBizAgreementApi, { ids }, '删除所选公示/协议内容表')
  proTableRef.value?.clearSelection()
  proTableRef.value?.getTableList()
}
// 导入
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>()
const importData = () => {
  const params = {
    title: '公示/协议内容表',
    templateName: '公示/协议内容表',
    tempApi: downloadTemplate,
    importApi: importBizAgreementExcelApi,
    getTableList: proTableRef.value?.getTableList
  }
  ImportExcelRef.value?.acceptParams(params)
}
// 导出
const downloadFile = async () => {
  let newParams = formatParams(proTableRef.value?.searchParam as BizAgreementQuery);
  useDownload(exportBizAgreementExcelApi, "公示/协议内容表", newParams);
};
</script>