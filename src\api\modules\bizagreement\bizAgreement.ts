import http from '@/api'
import { ADMIN_MODULE } from '@/api/helper/prefix'
import type { IPage } from '@/api/types';
import type { BizAgreementQuery, BizAgreementRow, BizAgreementForm } from '@/api/types/bizagreement/bizAgreement'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getBizAgreementListApi = (params: BizAgreementQuery) => {
  return http.get<IPage<BizAgreementRow>>(ADMIN_MODULE + `/biz-agreement`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createBizAgreementApi = (params: BizAgreementForm) => {
  return http.post(ADMIN_MODULE + `/biz-agreement`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateBizAgreementApi = (params: BizAgreementForm) => {
  return http.put(ADMIN_MODULE + `/biz-agreement`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeBizAgreementApi = (params: { ids: (string | number)[] }) => {
 return http.delete(ADMIN_MODULE + `/biz-agreement`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getBizAgreementDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<BizAgreementRow>(ADMIN_MODULE + `/biz-agreement/${id}`)
}

/**
* 导入excel
* @param params
*/
export const importBizAgreementExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<any> | undefined) => {
  return http.upload(ADMIN_MODULE + `/biz-agreement/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportBizAgreementExcelApi  = (params: BizAgreementQuery) => {
  return http.download(ADMIN_MODULE + `/biz-agreement/export`, params)
}