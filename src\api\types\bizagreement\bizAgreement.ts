import type { IPageQuery } from '@/api/types'

// 查询条件
export type BizAgreementQuery = IPageQuery & {
    agreementContent?: string
    isNot?: string
    sortNum?: number
  }

// 编辑form表单
export type BizAgreementForm = {
    agreementId?: number
    agreementContent?: string
    isNot?: string
    remark?: string
    sortNum?: number
 }

// list或detail返回结构
export type BizAgreementRow = {
    agreementId?: number
    agreementContent?: string
    isNot?: string
    remark?: string
    sortNum?: number
  }

