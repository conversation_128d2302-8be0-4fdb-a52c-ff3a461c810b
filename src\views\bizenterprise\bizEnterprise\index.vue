<template>
  <div class="table-box">
    <ProTable
      ref="proTableRef"
      title="企业信息表"
      :indent="20"
      :columns="columns"
      :search-columns="searchColumns"
      :request-api="getTableList"
      row-key="enterpriseId"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary"
          v-auth="'biz.enterprise.create'"
          :icon="CirclePlus"
          @click="openAddEdit('新增企业信息表')"
        >
          新增
        </el-button>
        <el-button
          v-auth="'biz.enterprise.remove'"
          type="danger"
          :icon="Delete"
          plain
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          批量删除
        </el-button>
        <el-button
          v-auth="'biz.enterprise.import'"
          type="primary"
          :icon="Upload"
          plain
          @click="importData"
        >
          导入
        </el-button>
        <el-button
          v-auth="'biz.enterprise.export'"
          type="primary"
          :icon="Download"
          plain
          @click="downloadFile"
        >
          导出
        </el-button>
      </template>
      <template #operation="{ row }">
        <el-button
          v-auth="'biz.enterprise.update'"
          type="primary"
          link
          :icon="EditPen"
          @click="openAddEdit('编辑企业信息表', row, false)"
        >
          编辑
        </el-button>
        <el-button
            v-auth="'biz.enterprise.remove'"
          type="primary"
          link
          :icon="Delete"
          @click="deleteInfo(row)"
        >
          删除
        </el-button>
      </template>
    </ProTable>
    <BizEnterpriseForm ref="bizEnterpriseRef" />
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  CirclePlus,
  Delete,
  EditPen,
  Upload,
  Download,
} from '@element-plus/icons-vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  createBizEnterpriseApi,
  removeBizEnterpriseApi,
  updateBizEnterpriseApi,
  getBizEnterpriseListApi,
  getBizEnterpriseDetailApi,
  importBizEnterpriseExcelApi,
  exportBizEnterpriseExcelApi,
} from '@/api/modules/bizenterprise/bizEnterprise';
import { useHandleData } from '@/hooks/useHandleData';
import BizEnterpriseForm from '@/views/bizenterprise/bizEnterprise/components/BizEnterpriseForm.vue';
import type { ColumnProps, ProTableInstance, SearchProps } from '@/components/ProTable/interface';
import type { BizEnterpriseQuery, BizEnterpriseRow } from '@/api/types/bizenterprise/bizEnterprise';
import ImportExcel from '@/components/ImportExcel/index.vue';
import { downloadTemplate } from '@/api/modules/system/common';
import { useDownload } from "@/hooks/useDownload";
import { useDictOptions } from '@/hooks/useDictOptions';

defineOptions({
  name: 'BizEnterpriseView'
})
const proTableRef = ref<ProTableInstance>();
// 表格配置项
const columns: ColumnProps<BizEnterpriseRow>[] = [
  { type: 'selection', width: 80 },
  { prop: 'enterpriseName', label: '企业名称' },
  { prop: 'creditCode', label: '统一社会信用代码' },
  { prop: 'leaderContact', label: '负责人手机号' },
  { prop: 'createName', label: '创建人' },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'updateTime', label: '更新时间' },
  {
    prop: 'accountStatusCd',
    label: '账号状态',
    tag: true,
    enum: useDictOptions('account_status'),
    fieldNames: {
      label: 'codeName',
      value: 'id',
      tagType: 'callbackShowStyle'
    }
  },
  { prop: 'operation', label: '操作', width: 250, fixed: 'right' }
]
// 搜索条件项
const searchColumns: SearchProps[] = [
  { prop: 'enterpriseName', label: '企业名称', el: 'input' },
  { prop: 'leaderContact', label: '手机号', el: 'input' },
  {
    prop: 'accountStatusCd',
    label: '账号状态',
    el: 'select',
    fieldNames: {
      label: 'codeName',
      value: 'id',
      tagType: 'callbackShowStyle'
    }
  },
  { prop: 'creditCode', label: '企业信用代码', el: 'input' },
  // { prop: 'createTime',
  //   label: '创建时间',
  //   el: 'date-picker',
  //   span: 2,
  //   props: {
  //     type: "datetimerange",
  //     valueFormat: "YYYY-MM-DD HH:mm:ss"
  //   },
  // },
]
// 获取table列表
const getTableList = (params: BizEnterpriseQuery) => {
  let newParams = formatParams(params);
  return getBizEnterpriseListApi(newParams);
};
const formatParams = (params: BizEnterpriseQuery) =>{
  let newParams = JSON.parse(JSON.stringify(params));
  return newParams;
}
// 处理状态切换
const handleStatusChange = async (row: BizEnterpriseRow) => {
  const params = {
    enterpriseId: row.enterpriseId,
    accountStatusCd: row.accountStatusCd
  };
  await useHandleData(
    updateBizEnterpriseApi,
    params,
    `${row.accountStatusCd === 'T' ? '启用' : '禁用'}【${row.enterpriseName}】企业账号状态`
  );
  proTableRef.value?.getTableList();
}
// 打开 drawer(新增、查看、编辑)
const bizEnterpriseRef = ref<InstanceType<typeof BizEnterpriseForm>>()
const openAddEdit = async(title: string, row: any = {}, isAdd = true) => {
  if (!isAdd) {
    const record = await getBizEnterpriseDetailApi({ id: row?.enterpriseId })
    row = record?.data
  }
  const params = {
    title,
    row: { ...row },
    api: isAdd ? createBizEnterpriseApi : updateBizEnterpriseApi,
    getTableList: proTableRef.value?.getTableList
  }
  bizEnterpriseRef.value?.acceptParams(params)
}
// 删除信息
const deleteInfo = async (params: BizEnterpriseRow) => {
  await useHandleData(
    removeBizEnterpriseApi,
    { ids: [params.enterpriseId] },
    `删除【${params.enterpriseId}】企业信息表`
  )
  proTableRef.value?.getTableList()
}
// 批量删除信息
const batchDelete = async (ids: (string | number)[]) => {
  await useHandleData(removeBizEnterpriseApi, { ids }, '删除所选企业信息表')
  proTableRef.value?.clearSelection()
  proTableRef.value?.getTableList()
}
// 导入
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>()
const importData = () => {
  const params = {
    title: '企业信息表',
    templateName: '企业信息表',
    tempApi: downloadTemplate,
    importApi: importBizEnterpriseExcelApi,
    getTableList: proTableRef.value?.getTableList
  }
  ImportExcelRef.value?.acceptParams(params)
}
// 导出
const downloadFile = async () => {
  let newParams = formatParams(proTableRef.value?.searchParam as BizEnterpriseQuery);
  useDownload(exportBizEnterpriseExcelApi, "企业信息表", newParams);
};
</script>