# 本地环境
VITE_USER_NODE_ENV=development

# 公共基础路径
VITE_PUBLIC_PATH=/

# 开发环境接口地址
VITE_API_URL=http://127.0.0.1:9991/api
## 启用WebSocket连接
## 若需启用WebSocket，请设置VITE_SOCKET_URL为有效的WebSocket地址
## 若不设置或留空，WebSocket功能将不会启用。例：
# VITE_SOCKET_URL=ws://127.0.0.1:9993/socket
VITE_SOCKET_URL=ws://127.0.0.1:9993/socket
VITE_APP_CLIENT_ID ="195da9fcce574852b850068771cde034"
# 是否对admin（超管）用户放行前端按钮权限验证，默认放行
VITE_ADMIN_BYPASS_PERMISSION=true
