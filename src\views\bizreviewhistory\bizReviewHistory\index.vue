<template>
  <div class="table-box">
    <ProTable
      ref="proTableRef"
      title="审核历史表"
      :indent="20"
      :columns="columns"
      :search-columns="searchColumns"
      :request-api="getTableList"
      row-key="reviewHistoryId"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary"
          v-auth="'biz.review.history.create'"
          :icon="CirclePlus"
          @click="openAddEdit('新增审核历史表')"
        >
          新增
        </el-button>
        <el-button
          v-auth="'biz.review.history.remove'"
          type="danger"
          :icon="Delete"
          plain
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          批量删除
        </el-button>
        <el-button
          v-auth="'biz.review.history.import'"
          type="primary"
          :icon="Upload"
          plain
          @click="importData"
        >
          导入
        </el-button>
        <el-button
          v-auth="'biz.review.history.export'"
          type="primary"
          :icon="Download"
          plain
          @click="downloadFile"
        >
          导出
        </el-button>
      </template>
      <template #operation="{ row }">
        <el-button
          v-auth="'biz.review.history.update'"
          type="primary"
          link
          :icon="EditPen"
          @click="openAddEdit('编辑审核历史表', row, false)"
        >
          编辑
        </el-button>
        <el-button
            v-auth="'biz.review.history.remove'"
          type="primary"
          link
          :icon="Delete"
          @click="deleteInfo(row)"
        >
          删除
        </el-button>
      </template>
    </ProTable>
    <BizReviewHistoryForm ref="bizReviewHistoryRef" />
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  CirclePlus,
  Delete,
  EditPen,
  Upload,
  Download,
} from '@element-plus/icons-vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  createBizReviewHistoryApi,
  removeBizReviewHistoryApi,
  updateBizReviewHistoryApi,
  getBizReviewHistoryListApi,
  getBizReviewHistoryDetailApi,
  importBizReviewHistoryExcelApi,
  exportBizReviewHistoryExcelApi,
} from '@/api/modules/bizreviewhistory/bizReviewHistory';
import { useHandleData } from '@/hooks/useHandleData';
import BizReviewHistoryForm from '@/views/bizreviewhistory/bizReviewHistory/components/BizReviewHistoryForm.vue';
import { useDictOptions } from '@/hooks/useDictOptions';
import type { ColumnProps, ProTableInstance, SearchProps } from '@/components/ProTable/interface';
import type { BizReviewHistoryQuery, BizReviewHistoryRow } from '@/api/types/bizreviewhistory/bizReviewHistory';
import ImportExcel from '@/components/ImportExcel/index.vue';
import { downloadTemplate } from '@/api/modules/system/common';
import { useDownload } from "@/hooks/useDownload";
defineOptions({
  name: 'BizReviewHistoryView'
})
const proTableRef = ref<ProTableInstance>();
// 表格配置项
const columns: ColumnProps<BizReviewHistoryRow>[] = [
  { type: 'selection', width: 80 },
  { prop: 'reviewStatus',
    label: '审核状态',
    tag: true,
    enum: useDictOptions('review_status'),
    fieldNames: {
      label: "codeName",
      value: "id",
      tagType: "callbackShowStyle"
    },
  },
  { prop: 'auditor', label: '审核人' },
  { prop: 'pubId', label: '发布内容ID' },
  { prop: 'remark', label: '备注' },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'operation', label: '操作', width: 250, fixed: 'right' }
]
// 搜索条件项
const searchColumns: SearchProps[] = [
  { prop: 'reviewStatus',
    label: '审核状态',
    el: 'select',
    enum: useDictOptions('review_status'),
    fieldNames: {
      label: "codeName",
      value: "id",
      tagType: "callbackShowStyle"
    },
  },
  { prop: 'auditor', label: '审核人', el: 'input' },
  { prop: 'pubId', label: '发布内容ID', el: 'input' },
  { prop: 'createTime',
    label: '创建时间',
    el: 'date-picker',
    span: 2,
    props: {
      type: "datetimerange",
      valueFormat: "YYYY-MM-DD HH:mm:ss"
    },
  },
]
// 获取table列表
const getTableList = (params: BizReviewHistoryQuery) => {
  let newParams = formatParams(params);
  return getBizReviewHistoryListApi(newParams);
};
const formatParams = (params: BizReviewHistoryQuery) =>{
  let newParams = JSON.parse(JSON.stringify(params));
  if(newParams.updateTime) {
    newParams.updateTimeStart = newParams.updateTime[0];
    newParams.updateTimeEnd = newParams.updateTime[1];
    delete newParams.updateTime;
  }

  return newParams;
}
// 打开 drawer(新增、查看、编辑)
const bizReviewHistoryRef = ref<InstanceType<typeof BizReviewHistoryForm>>()
const openAddEdit = async(title: string, row: any = {}, isAdd = true) => {
  if (!isAdd) {
    const record = await getBizReviewHistoryDetailApi({ id: row?.reviewHistoryId })
    row = record?.data
  }
  const params = {
    title,
    row: { ...row },
    api: isAdd ? createBizReviewHistoryApi : updateBizReviewHistoryApi,
    getTableList: proTableRef.value?.getTableList
  }
  bizReviewHistoryRef.value?.acceptParams(params)
}
// 删除信息
const deleteInfo = async (params: BizReviewHistoryRow) => {
  await useHandleData(
    removeBizReviewHistoryApi,
    { ids: [params.reviewHistoryId] },
    `删除【${params.reviewHistoryId}】审核历史表`
  )
  proTableRef.value?.getTableList()
}
// 批量删除信息
const batchDelete = async (ids: (string | number)[]) => {
  await useHandleData(removeBizReviewHistoryApi, { ids }, '删除所选审核历史表')
  proTableRef.value?.clearSelection()
  proTableRef.value?.getTableList()
}
// 导入
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>()
const importData = () => {
  const params = {
    title: '审核历史表',
    templateName: '审核历史表',
    tempApi: downloadTemplate,
    importApi: importBizReviewHistoryExcelApi,
    getTableList: proTableRef.value?.getTableList
  }
  ImportExcelRef.value?.acceptParams(params)
}
// 导出
const downloadFile = async () => {
  let newParams = formatParams(proTableRef.value?.searchParam as BizReviewHistoryQuery);
  useDownload(exportBizReviewHistoryExcelApi, "审核历史表", newParams);
};
</script>