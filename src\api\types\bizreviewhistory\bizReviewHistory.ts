import type { IPageQuery } from '@/api/types'

// 查询条件
export type BizReviewHistoryQuery = IPageQuery & {
    reviewStatus?: string
    auditor?: number
    pubId?: number
    createTime?: string
  }

// 编辑form表单
export type BizReviewHistoryForm = {
    reviewStatus?: string
    auditor?: number
    remark?: string
 }

// list或detail返回结构
export type BizReviewHistoryRow = {
    reviewStatus?: string
    auditor?: number
    pubId?: number
    remark?: string
    createTime?: string
  }

