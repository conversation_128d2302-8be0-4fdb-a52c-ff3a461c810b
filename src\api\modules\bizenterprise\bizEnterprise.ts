import http from '@/api'
import { ADMIN_MODULE } from '@/api/helper/prefix'
import type { IPage } from '@/api/types';
import type { BizEnterpriseQuery, BizEnterpriseRow, BizEnterpriseForm } from '@/api/types/bizenterprise/bizEnterprise'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getBizEnterpriseListApi = (params: BizEnterpriseQuery) => {
  return http.get<IPage<BizEnterpriseRow>>(ADMIN_MODULE + `/biz-enterprise`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createBizEnterpriseApi = (params: BizEnterpriseForm) => {
  return http.post(ADMIN_MODULE + `/biz-enterprise`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateBizEnterpriseApi = (params: BizEnterpriseForm) => {
  return http.put(ADMIN_MODULE + `/biz-enterprise`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeBizEnterpriseApi = (params: { ids: (string | number)[] }) => {
 return http.delete(ADMIN_MODULE + `/biz-enterprise`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getBizEnterpriseDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<BizEnterpriseRow>(ADMIN_MODULE + `/biz-enterprise/${id}`)
}

/**
* 导入excel
* @param params
*/
export const importBizEnterpriseExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<any> | undefined) => {
  return http.upload(ADMIN_MODULE + `/biz-enterprise/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportBizEnterpriseExcelApi  = (params: BizEnterpriseQuery) => {
  return http.download(ADMIN_MODULE + `/biz-enterprise/export`, params)
}