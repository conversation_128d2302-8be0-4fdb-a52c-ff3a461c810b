<template>
  <div class="table-box">
    <ProTable
      ref="proTableRef"
      title="内容发布表"
      :indent="20"
      :columns="columns"
      :search-columns="searchColumns"
      :request-api="getTableList"
      row-key="pubId"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary"
          v-auth="'biz.pub.content.create'"
          :icon="CirclePlus"
          @click="openAddEdit('新增内容发布表')"
        >
          新增
        </el-button>
        <el-button
          v-auth="'biz.pub.content.remove'"
          type="danger"
          :icon="Delete"
          plain
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          批量删除
        </el-button>
        <el-button
          v-auth="'biz.pub.content.import'"
          type="primary"
          :icon="Upload"
          plain
          @click="importData"
        >
          导入
        </el-button>
        <el-button
          v-auth="'biz.pub.content.export'"
          type="primary"
          :icon="Download"
          plain
          @click="downloadFile"
        >
          导出
        </el-button>
      </template>
      <template #operation="{ row }">
        <el-button
          v-auth="'biz.pub.content.update'"
          type="primary"
          link
          :icon="EditPen"
          @click="openAddEdit('编辑内容发布表', row, false)"
        >
          编辑
        </el-button>
        <el-button
            v-auth="'biz.pub.content.remove'"
          type="primary"
          link
          :icon="Delete"
          @click="deleteInfo(row)"
        >
          删除
        </el-button>
      </template>
    </ProTable>
    <BizPubContentForm ref="bizPubContentRef" />
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  CirclePlus,
  Delete,
  EditPen,
  Upload,
  Download,
} from '@element-plus/icons-vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  createBizPubContentApi,
  removeBizPubContentApi,
  updateBizPubContentApi,
  getBizPubContentListApi,
  getBizPubContentDetailApi,
  importBizPubContentExcelApi,
  exportBizPubContentExcelApi,
} from '@/api/modules/bizpubcontent/bizPubContent';
import { useHandleData } from '@/hooks/useHandleData';
import BizPubContentForm from '@/views/bizpubcontent/bizPubContent/components/BizPubContentForm.vue';
import { useDictOptions } from '@/hooks/useDictOptions';
import type { ColumnProps, ProTableInstance, SearchProps } from '@/components/ProTable/interface';
import type { BizPubContentQuery, BizPubContentRow } from '@/api/types/bizpubcontent/bizPubContent';
import ImportExcel from '@/components/ImportExcel/index.vue';
import { downloadTemplate } from '@/api/modules/system/common';
import { useDownload } from "@/hooks/useDownload";
defineOptions({
  name: 'BizPubContentView'
})
const proTableRef = ref<ProTableInstance>();
// 表格配置项
const columns: ColumnProps<BizPubContentRow>[] = [
  { type: 'selection', width: 80 },
  { prop: 'reviewStatus',
    label: '审核状态(码表)',
    tag: true,
    enum: useDictOptions('review_status'),
    fieldNames: {
      label: "codeName",
      value: "id",
      tagType: "callbackShowStyle"
    },
  },
  { prop: 'pubContent', label: '内容' },
  { prop: 'createId', label: '创建人ID' },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'updateTime', label: '更新时间' },
  { prop: 'operation', label: '操作', width: 250, fixed: 'right' }
]
// 搜索条件项
const searchColumns: SearchProps[] = [
  { prop: 'reviewStatus',
    label: '审核状态(码表)',
    el: 'select',
    enum: useDictOptions('review_status'),
    fieldNames: {
      label: "codeName",
      value: "id",
      tagType: "callbackShowStyle"
    },
  },
  { prop: 'pubContent', label: '内容', el: 'input' },
  { prop: 'createTime',
    label: '创建时间',
    el: 'date-picker',
    span: 2,
    props: {
      type: "datetimerange",
      valueFormat: "YYYY-MM-DD HH:mm:ss"
    },
  },
]
// 获取table列表
const getTableList = (params: BizPubContentQuery) => {
  let newParams = formatParams(params);
  return getBizPubContentListApi(newParams);
};
const formatParams = (params: BizPubContentQuery) =>{
  let newParams = JSON.parse(JSON.stringify(params));
  if(newParams.updateTime) {
    newParams.updateTimeStart = newParams.updateTime[0];
    newParams.updateTimeEnd = newParams.updateTime[1];
    delete newParams.updateTime;
  }

  return newParams;
}
// 打开 drawer(新增、查看、编辑)
const bizPubContentRef = ref<InstanceType<typeof BizPubContentForm>>()
const openAddEdit = async(title: string, row: any = {}, isAdd = true) => {
  if (!isAdd) {
    const record = await getBizPubContentDetailApi({ id: row?.pubId })
    row = record?.data
  }
  const params = {
    title,
    row: { ...row },
    api: isAdd ? createBizPubContentApi : updateBizPubContentApi,
    getTableList: proTableRef.value?.getTableList
  }
  bizPubContentRef.value?.acceptParams(params)
}
// 删除信息
const deleteInfo = async (params: BizPubContentRow) => {
  await useHandleData(
    removeBizPubContentApi,
    { ids: [params.pubId] },
    `删除【${params.pubId}】内容发布表`
  )
  proTableRef.value?.getTableList()
}
// 批量删除信息
const batchDelete = async (ids: (string | number)[]) => {
  await useHandleData(removeBizPubContentApi, { ids }, '删除所选内容发布表')
  proTableRef.value?.clearSelection()
  proTableRef.value?.getTableList()
}
// 导入
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>()
const importData = () => {
  const params = {
    title: '内容发布表',
    templateName: '内容发布表',
    tempApi: downloadTemplate,
    importApi: importBizPubContentExcelApi,
    getTableList: proTableRef.value?.getTableList
  }
  ImportExcelRef.value?.acceptParams(params)
}
// 导出
const downloadFile = async () => {
  let newParams = formatParams(proTableRef.value?.searchParam as BizPubContentQuery);
  useDownload(exportBizPubContentExcelApi, "内容发布表", newParams);
};
</script>