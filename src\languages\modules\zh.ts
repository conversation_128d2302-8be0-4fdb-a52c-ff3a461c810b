export default {
  home: {
    welcome: '欢迎使用'
  },
  tabs: {
    refresh: '刷新',
    maximize: '最大化',
    closeCurrent: '关闭当前',
    closeLeft: '关闭左侧',
    closeRight: '关闭右侧',
    closeOther: '关闭其它',
    closeAll: '关闭所有'
  },
  header: {
    componentSize: '组件大小',
    language: '国际化',
    theme: '全局主题',
    layoutConfig: '布局设置',
    primary: 'primary',
    darkMode: '暗黑模式',
    greyMode: '灰色模式',
    weakMode: '色弱模式',
    fullScreen: '全屏',
    exitFullScreen: '退出全屏',
    personalData: '个人信息',
    changePassword: '修改密码',
    logout: '退出登录'
  }
};
