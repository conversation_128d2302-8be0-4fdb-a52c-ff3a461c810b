{"name": "sz-admin", "version": "1.2.1-beta", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@eslint/js": "^9.26.0", "@highlightjs/vue-plugin": "^2.1.0", "@rushstack/eslint-patch": "^1.11.0", "@vueuse/core": "^10.11.1", "axios": "^1.9.0", "element-plus": "^2.9.9", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "^9.0.0", "globals": "^16.1.0", "highlight.js": "^11.11.1", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^3.2.3", "screenfull": "^6.0.2", "sortablejs": "^1.15.6", "typescript-eslint": "^8.32.0", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/node": "^18.19.99", "@types/node-forge": "^1.3.11", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.1", "node-forge": "^1.3.1", "npm-run-all2": "^6.2.6", "prettier": "^3.5.3", "sass": "~1.87.0", "typescript": "~5.2.2", "vite": "6.3.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^2.2.10"}}