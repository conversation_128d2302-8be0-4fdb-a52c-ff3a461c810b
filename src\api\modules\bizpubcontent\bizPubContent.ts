import http from '@/api'
import { ADMIN_MODULE } from '@/api/helper/prefix'
import type { IPage } from '@/api/types';
import type { BizPubContentQuery, BizPubContentRow, BizPubContentForm } from '@/api/types/bizpubcontent/bizPubContent'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getBizPubContentListApi = (params: BizPubContentQuery) => {
  return http.get<IPage<BizPubContentRow>>(ADMIN_MODULE + `/biz-pub-content`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createBizPubContentApi = (params: BizPubContentForm) => {
  return http.post(ADMIN_MODULE + `/biz-pub-content`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateBizPubContentApi = (params: BizPubContentForm) => {
  return http.put(ADMIN_MODULE + `/biz-pub-content`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeBizPubContentApi = (params: { ids: (string | number)[] }) => {
 return http.delete(ADMIN_MODULE + `/biz-pub-content`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getBizPubContentDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<BizPubContentRow>(ADMIN_MODULE + `/biz-pub-content/${id}`)
}

/**
* 导入excel
* @param params
*/
export const importBizPubContentExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<any> | undefined) => {
  return http.upload(ADMIN_MODULE + `/biz-pub-content/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportBizPubContentExcelApi  = (params: BizPubContentQuery) => {
  return http.download(ADMIN_MODULE + `/biz-pub-content/export`, params)
}