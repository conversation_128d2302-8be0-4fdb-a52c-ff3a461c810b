import http from '@/api'
import { ADMIN_MODULE } from '@/api/helper/prefix'
import type { IPage } from '@/api/types';
import type { BizReviewHistoryQuery, BizReviewHistoryRow, BizReviewHistoryForm } from '@/api/types/bizreviewhistory/bizReviewHistory'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getBizReviewHistoryListApi = (params: BizReviewHistoryQuery) => {
  return http.get<IPage<BizReviewHistoryRow>>(ADMIN_MODULE + `/biz-review-history`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createBizReviewHistoryApi = (params: BizReviewHistoryForm) => {
  return http.post(ADMIN_MODULE + `/biz-review-history`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateBizReviewHistoryApi = (params: BizReviewHistoryForm) => {
  return http.put(ADMIN_MODULE + `/biz-review-history`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeBizReviewHistoryApi = (params: { ids: (string | number)[] }) => {
 return http.delete(ADMIN_MODULE + `/biz-review-history`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getBizReviewHistoryDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<BizReviewHistoryRow>(ADMIN_MODULE + `/biz-review-history/${id}`)
}

/**
* 导入excel
* @param params
*/
export const importBizReviewHistoryExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<any> | undefined) => {
  return http.upload(ADMIN_MODULE + `/biz-review-history/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportBizReviewHistoryExcelApi  = (params: BizReviewHistoryQuery) => {
  return http.download(ADMIN_MODULE + `/biz-review-history/export`, params)
}