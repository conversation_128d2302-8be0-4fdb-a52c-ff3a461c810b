import http from '@/api'
import { ADMIN_MODULE } from '@/api/helper/prefix'
import type { IPage } from '@/api/types';
import type { BizCommercialQuery, BizCommercialRow, BizCommercialForm } from '@/api/types/bizcommercial/bizCommercial'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getBizCommercialListApi = (params: BizCommercialQuery) => {
  return http.get<IPage<BizCommercialRow>>(ADMIN_MODULE + `/biz-commercial`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createBizCommercialApi = (params: BizCommercialForm) => {
  return http.post(ADMIN_MODULE + `/biz-commercial`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateBizCommercialApi = (params: BizCommercialForm) => {
  return http.put(ADMIN_MODULE + `/biz-commercial`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeBizCommercialApi = (params: { ids: (string | number)[] }) => {
 return http.delete(ADMIN_MODULE + `/biz-commercial`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getBizCommercialDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<BizCommercialRow>(ADMIN_MODULE + `/biz-commercial/${id}`)
}

/**
* 导入excel
* @param params
*/
export const importBizCommercialExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<any> | undefined) => {
  return http.upload(ADMIN_MODULE + `/biz-commercial/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportBizCommercialExcelApi  = (params: BizCommercialQuery) => {
  return http.download(ADMIN_MODULE + `/biz-commercial/export`, params)
}