export default {
  home: {
    welcome: 'Welcome'
  },
  tabs: {
    refresh: 'Refresh',
    maximize: 'Maximize',
    closeCurrent: 'Close current',
    closeLeft: 'Close Left',
    closeRight: 'Close Right',
    closeOther: 'Close other',
    closeAll: 'Close All'
  },
  header: {
    componentSize: 'Component size',
    language: 'Language',
    theme: 'theme',
    layoutConfig: 'Layout config',
    primary: 'primary',
    darkMode: 'Dark Mode',
    greyMode: 'Grey mode',
    weakMode: 'Weak mode',
    fullScreen: 'Full Screen',
    exitFullScreen: 'Exit Full Screen',
    personalData: 'Personal Data',
    changePassword: 'Change Password',
    logout: 'Logout'
  }
};
