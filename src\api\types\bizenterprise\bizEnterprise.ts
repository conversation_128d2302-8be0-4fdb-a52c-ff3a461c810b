import type { IPageQuery } from '@/api/types'

// 查询条件
export type BizEnterpriseQuery = IPageQuery & {
    enterpriseName?: string
    creditCode?: string
    leaderContact?: string
    createTime?: string
  }

// 编辑form表单
export type BizEnterpriseForm = {
    enterpriseId?: number
    enterpriseName?: string
    creditCode?: string
    leaderContact?: string
    accountStatusCd?: string
    remark?: string
 }

// list或detail返回结构
export type BizEnterpriseRow = {
    enterpriseId?: number
    enterpriseName?: string
    creditCode?: string
    leaderContact?: string
    accountStatusCd?: string
    createId?: number
    createName?: string
    createTime?: string
    updateTime?: string
  }

